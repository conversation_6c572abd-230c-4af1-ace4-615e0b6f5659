package dev.meanmail.plugin.githubActions.parser

import org.junit.Assert.*
import org.junit.Test

class WorkflowParserTest {

    private val parser = WorkflowParser()

    @Test
    fun `should parse simple workflow`() {
        val yaml = """
            name: Test Workflow
            "on": push
            jobs:
              test:
                runs-on: ubuntu-latest
                steps:
                  - name: Checkout
                    uses: actions/checkout@v4
                  - name: Run tests
                    run: npm test
        """.trimIndent()

        val workflow = parser.parse(yaml)

        assertNotNull(workflow)
        assertEquals("Test Workflow", workflow!!.name)
        assertNotNull("Push trigger should not be null", workflow.on.push)
        assertEquals(1, workflow.jobs.size)

        val testJob = workflow.jobs["test"]
        assertNotNull(testJob)
        assertEquals(listOf("ubuntu-latest"), testJob!!.runsOn)
        assertEquals(2, testJob.steps.size)

        val firstStep = testJob.steps[0]
        assertEquals("Checkout", firstStep.name)
        assertEquals("actions/checkout@v4", firstStep.uses)

        val secondStep = testJob.steps[1]
        assertEquals("Run tests", secondStep.name)
        assertEquals("npm test", secondStep.run)
    }

    @Test
    fun `should parse complex triggers`() {
        val yaml = """
            name: Complex Workflow
            "on":
              push:
                branches: [main, develop]
                paths-ignore: ['**.md']
              pull_request:
                types: [opened, synchronize]
              schedule:
                - cron: '0 2 * * 1'
              workflow_dispatch:
                inputs:
                  environment:
                    description: 'Environment'
                    required: true
                    default: 'staging'
            jobs:
              test:
                runs-on: ubuntu-latest
                steps:
                  - run: echo "test"
        """.trimIndent()

        val workflow = parser.parse(yaml)

        assertNotNull(workflow)
        assertEquals("Complex Workflow", workflow!!.name)

        // Check push trigger
        assertNotNull(workflow.on.push)
        assertEquals(listOf("main", "develop"), workflow.on.push!!.branches)
        assertEquals(listOf("**.md"), workflow.on.push!!.pathsIgnore)

        // Check pull request trigger
        assertNotNull(workflow.on.pullRequest)
        assertEquals(listOf("opened", "synchronize"), workflow.on.pullRequest!!.types)

        // Check schedule trigger
        assertEquals(1, workflow.on.schedule.size)
        assertEquals("0 2 * * 1", workflow.on.schedule[0].cron)

        // Check workflow dispatch trigger
        assertNotNull(workflow.on.workflowDispatch)
        assertEquals(1, workflow.on.workflowDispatch!!.inputs.size)
        val envInput = workflow.on.workflowDispatch!!.inputs["environment"]
        assertNotNull(envInput)
        assertEquals("Environment", envInput!!.description)
        assertTrue(envInput.required)
        assertEquals("staging", envInput.default)
    }

    @Test
    fun `should parse job with matrix strategy`() {
        val yaml = """
            name: Matrix Workflow
            "on": push
            jobs:
              test:
                runs-on: ubuntu-latest
                strategy:
                  matrix:
                    node-version: [16, 18, 20]
                    os: [ubuntu-latest, windows-latest]
                  fail-fast: false
                  max-parallel: 4
                steps:
                  - run: echo "test"
        """.trimIndent()

        val workflow = parser.parse(yaml)

        assertNotNull(workflow)
        val testJob = workflow!!.jobs["test"]
        assertNotNull(testJob)

        val strategy = testJob!!.strategy
        assertNotNull(strategy)
        assertFalse(strategy!!.failFast)
        assertEquals(4, strategy.maxParallel)

        val matrix = strategy.matrix
        assertTrue(matrix.containsKey("node-version"))
        assertTrue(matrix.containsKey("os"))
    }

    @Test
    fun `should handle invalid yaml gracefully`() {
        val invalidYaml = """
            name: Invalid Workflow
            "on": push
            jobs:
              test:
                runs-on: ubuntu-latest
                steps:
                  - name: Invalid step
                    invalid_key: [unclosed list
        """.trimIndent()

        val workflow = parser.parse(invalidYaml)

        // Should return null for invalid YAML
        assertNull(workflow)
    }

    @Test
    fun `should parse job with needs and environment`() {
        val yaml = """
            name: Deployment Workflow
            "on": push
            jobs:
              build:
                runs-on: ubuntu-latest
                steps:
                  - run: echo "build"
              deploy:
                runs-on: ubuntu-latest
                needs: build
                environment:
                  name: production
                  url: https://example.com
                steps:
                  - run: echo "deploy"
        """.trimIndent()

        val workflow = parser.parse(yaml)

        assertNotNull(workflow)
        assertEquals(2, workflow!!.jobs.size)

        val deployJob = workflow.jobs["deploy"]
        assertNotNull(deployJob)
        assertEquals(listOf("build"), deployJob!!.needs)

        val environment = deployJob.environment
        assertNotNull(environment)
        assertEquals("production", environment!!.name)
        assertEquals("https://example.com", environment.url)
    }
}
