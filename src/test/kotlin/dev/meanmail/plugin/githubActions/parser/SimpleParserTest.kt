package dev.meanmail.plugin.githubActions.parser

import org.junit.Test

class SimpleParserTest {

    private val parser = WorkflowParser()

    @Test
    fun `debug yaml parsing directly`() {
        val yamlContent = """
            name: Test Workflow
            "on": push
            jobs:
              test:
                runs-on: ubuntu-latest
                steps:
                  - name: Checkout
                    uses: actions/checkout@v4
        """.trimIndent()

        // Test our parser
        val workflow = parser.parse(yamlContent)

        // Use assertions
        assert(workflow != null) { "Workflow should not be null" }
        assert(workflow!!.name == "Test Workflow") { "Expected name 'Test Workflow', got '${workflow.name}'" }
        assert(workflow.on.push != null) { "Push trigger should not be null. Triggers: ${workflow.on}" }
        assert(workflow.jobs.size == 1) { "Expected 1 job, got ${workflow.jobs.size}" }
    }
}
