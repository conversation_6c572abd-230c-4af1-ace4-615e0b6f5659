package dev.meanmail.plugin.githubActions.html

import dev.meanmail.plugin.githubActions.model.*
import org.junit.Assert.*
import org.junit.Test

class WorkflowHtmlGeneratorTest {

    private val generator = WorkflowHtmlGenerator()

    @Test
    fun `should generate HTML for simple workflow`() {
        val workflow = WorkflowModel(
            name = "Test Workflow",
            on = WorkflowTriggers(push = PushTrigger()),
            jobs = mapOf(
                "test" to Job(
                    name = "Run Tests",
                    runsOn = listOf("ubuntu-latest"),
                    steps = listOf(
                        Step(name = "Checkout", uses = "actions/checkout@v4"),
                        Step(name = "Run tests", run = "npm test")
                    )
                )
            )
        )

        val html = generator.generateHtml(workflow)

        // Check that HTML contains expected elements
        assertTrue("HTML should contain DOCTYPE", html.contains("<!DOCTYPE html>"))
        assertTrue("HTML should contain workflow title", html.contains("Test Workflow"))
        assertTrue("HTML should contain D3.js script", html.contains("d3.min.js"))
        assertTrue("HTML should contain d3-dag script", html.contains("d3-dag.min.js"))
        assertTrue("HTML should contain SVG element", html.contains("<svg id=\"dag-svg\""))
        assertTrue("HTML should contain workflow data", html.contains("workflowData"))
    }

    @Test
    fun `should generate HTML for complex workflow with dependencies`() {
        val workflow = WorkflowModel(
            name = "Complex Workflow",
            on = WorkflowTriggers(
                push = PushTrigger(branches = listOf("main")),
                pullRequest = PullRequestTrigger(types = listOf("opened"))
            ),
            jobs = mapOf(
                "build" to Job(
                    name = "Build",
                    runsOn = listOf("ubuntu-latest"),
                    steps = listOf(Step(name = "Build", run = "npm run build"))
                ),
                "test" to Job(
                    name = "Test",
                    runsOn = listOf("ubuntu-latest"),
                    needs = listOf("build"),
                    steps = listOf(Step(name = "Test", run = "npm test"))
                ),
                "deploy" to Job(
                    name = "Deploy",
                    runsOn = listOf("ubuntu-latest"),
                    needs = listOf("test"),
                    environment = JobEnvironment(name = "production"),
                    steps = listOf(Step(name = "Deploy", run = "deploy.sh"))
                )
            )
        )

        val html = generator.generateHtml(workflow)

        // Check that HTML contains expected elements
        assertTrue("HTML should contain workflow name", html.contains("Complex Workflow"))
        assertTrue("HTML should contain build job", html.contains("Build"))
        assertTrue("HTML should contain test job", html.contains("Test"))
        assertTrue("HTML should contain deploy job", html.contains("Deploy"))
        assertTrue("HTML should contain job dependencies", html.contains("parentIds"))
    }

    @Test
    fun `should generate HTML for workflow with reusable workflow`() {
        val workflow = WorkflowModel(
            name = "Reusable Workflow Test",
            on = WorkflowTriggers(workflowDispatch = WorkflowDispatchTrigger()),
            jobs = mapOf(
                "call-reusable" to Job(
                    name = "Call Reusable Workflow",
                    uses = "./.github/workflows/reusable.yml",
                    with = mapOf("environment" to "staging")
                )
            )
        )

        val html = generator.generateHtml(workflow)

        // Check that HTML contains expected elements for reusable workflow
        assertTrue("HTML should contain reusable workflow job", html.contains("Call Reusable Workflow"))
        assertTrue("HTML should contain reusable workflow icon", html.contains("🔄"))
        assertTrue("HTML should contain purple color for reusable workflow", html.contains("#8957e5"))
    }

    @Test
    fun `should handle empty workflow gracefully`() {
        val workflow = WorkflowModel(
            name = "Empty Workflow",
            on = WorkflowTriggers(),
            jobs = emptyMap()
        )

        val html = generator.generateHtml(workflow)

        // Should still generate valid HTML
        assertTrue("HTML should contain DOCTYPE", html.contains("<!DOCTYPE html>"))
        assertTrue("HTML should contain workflow name", html.contains("Empty Workflow"))
        assertTrue("HTML should contain trigger node", html.contains("trigger"))
    }

    @Test
    fun `should handle null workflow`() {
        val html = generator.generateHtml(null)

        // Should generate error HTML
        assertTrue("HTML should contain error message", html.contains("Invalid or empty workflow file"))
        assertTrue("HTML should be valid HTML", html.contains("<!DOCTYPE html>"))
    }

    @Test
    fun `should escape special characters in job names`() {
        val workflow = WorkflowModel(
            name = "Test \"Quotes\" & Ampersands",
            on = WorkflowTriggers(push = PushTrigger()),
            jobs = mapOf(
                "test" to Job(
                    name = "Test with \"quotes\" & <tags>",
                    runsOn = listOf("ubuntu-latest"),
                    steps = listOf(Step(name = "Test", run = "echo 'test'"))
                )
            )
        )

        val html = generator.generateHtml(workflow)

        // Check that special characters are properly escaped
        assertTrue("HTML should be valid", html.contains("<!DOCTYPE html>"))
        // The exact escaping depends on the implementation, but HTML should be valid
        assertFalse("HTML should not contain unescaped quotes in JSON", html.contains("\"quotes\""))
    }
}
