package dev.meanmail.plugin.githubActions.parser

import dev.meanmail.plugin.githubActions.model.*
import org.yaml.snakeyaml.Yaml
import java.io.StringReader

/**
 * Parser for GitHub Actions workflow YAML files
 */
class WorkflowParser {

    private val yaml = Yaml()

    /**
     * Parse workflow YAML content into WorkflowModel
     */
    fun parse(yamlContent: String): WorkflowModel? {
        return try {
            val data = yaml.load<Map<String, Any>>(StringReader(yamlContent)) ?: return null
            parseWorkflow(data)
        } catch (e: Exception) {
            // Log error and return null for invalid YAML
            null
        }
    }

    private fun parseWorkflow(data: Map<String, Any>): WorkflowModel {
        return WorkflowModel(
            name = data["name"] as? String,
            on = parseWorkflowTriggers(data["on"]),
            env = parseStringMap(data["env"]),
            defaults = parseWorkflowDefaults(data["defaults"]),
            concurrency = parseWorkflowConcurrency(data["concurrency"]),
            jobs = parseJobs(data["jobs"])
        )
    }

    private fun parseWorkflowTriggers(onData: Any?): WorkflowTriggers {
        when (onData) {
            is String -> {
                // Simple trigger like "push"
                return when (onData) {
                    "push" -> WorkflowTriggers(push = PushTrigger())
                    "pull_request" -> WorkflowTriggers(pullRequest = PullRequestTrigger())
                    "workflow_dispatch" -> WorkflowTriggers(workflowDispatch = WorkflowDispatchTrigger())
                    else -> WorkflowTriggers(other = mapOf(onData to true))
                }
            }

            is List<*> -> {
                // List of simple triggers
                var push: PushTrigger? = null
                var pullRequest: PullRequestTrigger? = null
                var workflowDispatch: WorkflowDispatchTrigger? = null
                val otherTriggers = mutableMapOf<String, Any>()

                onData.forEach { trigger ->
                    when (trigger as? String) {
                        "push" -> push = PushTrigger()
                        "pull_request" -> pullRequest = PullRequestTrigger()
                        "workflow_dispatch" -> workflowDispatch = WorkflowDispatchTrigger()
                        else -> trigger?.let { otherTriggers[it.toString()] = true }
                    }
                }
                return WorkflowTriggers(
                    push = push,
                    pullRequest = pullRequest,
                    workflowDispatch = workflowDispatch,
                    other = otherTriggers
                )
            }

            is Map<*, *> -> {
                val data = onData as Map<String, Any>
                return WorkflowTriggers(
                    push = data["push"]?.let {
                        when (it) {
                            is Boolean -> if (it) PushTrigger() else null
                            else -> parsePushTrigger(it)
                        }
                    },
                    pullRequest = data["pull_request"]?.let {
                        when (it) {
                            is Boolean -> if (it) PullRequestTrigger() else null
                            else -> parsePullRequestTrigger(it)
                        }
                    },
                    schedule = parseScheduleTriggers(data["schedule"]),
                    workflowDispatch = data["workflow_dispatch"]?.let {
                        when (it) {
                            is Boolean -> if (it) WorkflowDispatchTrigger() else null
                            else -> parseWorkflowDispatchTrigger(it)
                        }
                    },
                    workflowCall = data["workflow_call"]?.let {
                        when (it) {
                            is Boolean -> if (it) WorkflowCallTrigger() else null
                            else -> parseWorkflowCallTrigger(it)
                        }
                    },
                    other = data.filterKeys {
                        it !in setOf(
                            "push",
                            "pull_request",
                            "schedule",
                            "workflow_dispatch",
                            "workflow_call"
                        )
                    }
                )
            }

            else -> return WorkflowTriggers()
        }
    }

    private fun parsePushTrigger(data: Any): PushTrigger {
        if (data !is Map<*, *>) return PushTrigger()
        val map = data as Map<String, Any>
        return PushTrigger(
            branches = parseStringList(map["branches"]),
            branchesIgnore = parseStringList(map["branches-ignore"]),
            tags = parseStringList(map["tags"]),
            tagsIgnore = parseStringList(map["tags-ignore"]),
            paths = parseStringList(map["paths"]),
            pathsIgnore = parseStringList(map["paths-ignore"])
        )
    }

    private fun parsePullRequestTrigger(data: Any): PullRequestTrigger {
        if (data !is Map<*, *>) return PullRequestTrigger()
        val map = data as Map<String, Any>
        return PullRequestTrigger(
            types = parseStringList(map["types"]),
            branches = parseStringList(map["branches"]),
            branchesIgnore = parseStringList(map["branches-ignore"]),
            paths = parseStringList(map["paths"]),
            pathsIgnore = parseStringList(map["paths-ignore"])
        )
    }

    private fun parseScheduleTriggers(data: Any?): List<ScheduleTrigger> {
        if (data !is List<*>) return emptyList()
        return data.mapNotNull { item ->
            if (item is Map<*, *>) {
                val cron = (item as Map<String, Any>)["cron"] as? String
                cron?.let { ScheduleTrigger(it) }
            } else null
        }
    }

    private fun parseWorkflowDispatchTrigger(data: Any): WorkflowDispatchTrigger {
        if (data !is Map<*, *>) return WorkflowDispatchTrigger()
        val map = data as Map<String, Any>
        return WorkflowDispatchTrigger(
            inputs = parseWorkflowInputs(map["inputs"])
        )
    }

    private fun parseWorkflowCallTrigger(data: Any): WorkflowCallTrigger {
        if (data !is Map<*, *>) return WorkflowCallTrigger()
        val map = data as Map<String, Any>
        return WorkflowCallTrigger(
            inputs = parseWorkflowInputs(map["inputs"]),
            outputs = parseWorkflowOutputs(map["outputs"]),
            secrets = parseWorkflowSecrets(map["secrets"])
        )
    }

    private fun parseWorkflowInputs(data: Any?): Map<String, WorkflowInput> {
        if (data !is Map<*, *>) return emptyMap()
        val map = data as Map<String, Any>
        return map.mapValues { (_, value) ->
            if (value is Map<*, *>) {
                val inputMap = value as Map<String, Any>
                WorkflowInput(
                    description = inputMap["description"] as? String,
                    required = inputMap["required"] as? Boolean ?: false,
                    default = inputMap["default"] as? String,
                    type = inputMap["type"] as? String ?: "string"
                )
            } else {
                WorkflowInput()
            }
        }
    }

    private fun parseWorkflowOutputs(data: Any?): Map<String, WorkflowOutput> {
        if (data !is Map<*, *>) return emptyMap()
        val map = data as Map<String, Any>
        return map.mapValues { (_, value) ->
            if (value is Map<*, *>) {
                val outputMap = value as Map<String, Any>
                WorkflowOutput(
                    description = outputMap["description"] as? String,
                    value = outputMap["value"] as? String ?: ""
                )
            } else {
                WorkflowOutput(value = value.toString())
            }
        }
    }

    private fun parseWorkflowSecrets(data: Any?): Map<String, WorkflowSecret> {
        if (data !is Map<*, *>) return emptyMap()
        val map = data as Map<String, Any>
        return map.mapValues { (_, value) ->
            if (value is Map<*, *>) {
                val secretMap = value as Map<String, Any>
                WorkflowSecret(
                    description = secretMap["description"] as? String,
                    required = secretMap["required"] as? Boolean ?: false
                )
            } else {
                WorkflowSecret()
            }
        }
    }

    private fun parseWorkflowDefaults(data: Any?): WorkflowDefaults? {
        if (data !is Map<*, *>) return null
        val map = data as Map<String, Any>
        val run = map["run"]?.let { runData ->
            if (runData is Map<*, *>) {
                val runMap = runData as Map<String, Any>
                RunDefaults(
                    shell = runMap["shell"] as? String,
                    workingDirectory = runMap["working-directory"] as? String
                )
            } else null
        }
        return if (run != null) WorkflowDefaults(run) else null
    }

    private fun parseWorkflowConcurrency(data: Any?): WorkflowConcurrency? {
        return when (data) {
            is String -> WorkflowConcurrency(group = data)
            is Map<*, *> -> {
                val map = data as Map<String, Any>
                val group = map["group"] as? String ?: return null
                WorkflowConcurrency(
                    group = group,
                    cancelInProgress = map["cancel-in-progress"] as? Boolean ?: false
                )
            }

            else -> null
        }
    }

    private fun parseJobs(data: Any?): Map<String, Job> {
        if (data !is Map<*, *>) return emptyMap()
        val map = data as Map<String, Any>
        return map.mapValues { (_, jobData) ->
            parseJob(jobData)
        }
    }

    private fun parseJob(data: Any): Job {
        if (data !is Map<*, *>) return Job()
        val map = data as Map<String, Any>

        return Job(
            name = map["name"] as? String,
            runsOn = parseRunsOn(map["runs-on"]),
            needs = parseStringList(map["needs"]),
            if_ = map["if"] as? String,
            permissions = parseStringMap(map["permissions"]),
            environment = parseJobEnvironment(map["environment"]),
            concurrency = parseWorkflowConcurrency(map["concurrency"]),
            outputs = parseStringMap(map["outputs"]),
            env = parseStringMap(map["env"]),
            defaults = parseWorkflowDefaults(map["defaults"]),
            timeoutMinutes = map["timeout-minutes"] as? Int,
            strategy = parseJobStrategy(map["strategy"]),
            continueOnError = map["continue-on-error"] as? Boolean ?: false,
            container = parseJobContainer(map["container"]),
            services = parseJobServices(map["services"]),
            uses = map["uses"] as? String,
            with = parseAnyMap(map["with"]),
            secrets = parseStringMap(map["secrets"]),
            steps = parseSteps(map["steps"])
        )
    }

    private fun parseRunsOn(data: Any?): List<String> {
        return when (data) {
            is String -> listOf(data)
            is List<*> -> data.mapNotNull { it as? String }
            else -> emptyList()
        }
    }

    private fun parseJobEnvironment(data: Any?): JobEnvironment? {
        return when (data) {
            is String -> JobEnvironment(name = data)
            is Map<*, *> -> {
                val map = data as Map<String, Any>
                val name = map["name"] as? String ?: return null
                JobEnvironment(
                    name = name,
                    url = map["url"] as? String
                )
            }

            else -> null
        }
    }

    private fun parseJobStrategy(data: Any?): JobStrategy? {
        if (data !is Map<*, *>) return null
        val map = data as Map<String, Any>
        return JobStrategy(
            matrix = parseAnyMap(map["matrix"]),
            failFast = map["fail-fast"] as? Boolean ?: true,
            maxParallel = map["max-parallel"] as? Int
        )
    }

    private fun parseJobContainer(data: Any?): JobContainer? {
        return when (data) {
            is String -> JobContainer(image = data)
            is Map<*, *> -> {
                val map = data as Map<String, Any>
                val image = map["image"] as? String ?: return null
                JobContainer(
                    image = image,
                    credentials = parseStringMap(map["credentials"]),
                    env = parseStringMap(map["env"]),
                    ports = parseStringList(map["ports"]),
                    volumes = parseStringList(map["volumes"]),
                    options = map["options"] as? String
                )
            }

            else -> null
        }
    }

    private fun parseJobServices(data: Any?): Map<String, JobService> {
        if (data !is Map<*, *>) return emptyMap()
        val map = data as Map<String, Any>
        return map.mapValues { (_, serviceData) ->
            parseJobService(serviceData)
        }.filterValues { it != null }.mapValues { it.value!! }
    }

    private fun parseJobService(data: Any): JobService? {
        return when (data) {
            is String -> JobService(image = data)
            is Map<*, *> -> {
                val map = data as Map<String, Any>
                val image = map["image"] as? String ?: return null
                JobService(
                    image = image,
                    credentials = parseStringMap(map["credentials"]),
                    env = parseStringMap(map["env"]),
                    ports = parseStringList(map["ports"]),
                    volumes = parseStringList(map["volumes"]),
                    options = map["options"] as? String
                )
            }

            else -> null
        }
    }

    private fun parseSteps(data: Any?): List<Step> {
        if (data !is List<*>) return emptyList()
        return data.mapNotNull { stepData ->
            parseStep(stepData)
        }
    }

    private fun parseStep(data: Any?): Step? {
        if (data !is Map<*, *>) return null
        val map = data as Map<String, Any>

        return Step(
            id = map["id"] as? String,
            name = map["name"] as? String,
            uses = map["uses"] as? String,
            run = map["run"] as? String,
            with = parseAnyMap(map["with"]),
            env = parseStringMap(map["env"]),
            if_ = map["if"] as? String,
            continueOnError = map["continue-on-error"] as? Boolean ?: false,
            timeoutMinutes = map["timeout-minutes"] as? Int,
            shell = map["shell"] as? String,
            workingDirectory = map["working-directory"] as? String
        )
    }

    // Helper functions
    private fun parseStringList(data: Any?): List<String> {
        return when (data) {
            is String -> listOf(data)
            is List<*> -> data.mapNotNull { it as? String }
            else -> emptyList()
        }
    }

    private fun parseStringMap(data: Any?): Map<String, String> {
        if (data !is Map<*, *>) return emptyMap()
        return (data as Map<String, Any>).mapValues { it.value.toString() }
    }

    private fun parseAnyMap(data: Any?): Map<String, Any> {
        if (data !is Map<*, *>) return emptyMap()
        return data as Map<String, Any>
    }
}
