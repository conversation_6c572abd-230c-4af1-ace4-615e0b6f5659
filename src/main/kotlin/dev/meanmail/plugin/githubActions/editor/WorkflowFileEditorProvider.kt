package dev.meanmail.plugin.githubActions.editor

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.fileEditor.impl.text.PsiAwareTextEditorProvider
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import org.jdom.Element

/**
 * File editor provider for GitHub Actions workflow files
 * Creates a split editor with text and visual preview
 */
class WorkflowFileEditorProvider : FileEditorProvider, DumbAware {

    companion object {
        const val EDITOR_TYPE_ID = "github-actions-workflow-editor"
    }

    override fun accept(project: Project, file: VirtualFile): Boolean {
        val logger = thisLogger()
        logger.info("Checking file: ${file.path}")

        // Accept YAML files in .github/workflows directory
        if (!file.name.endsWith(".yml") && !file.name.endsWith(".yaml")) {
            logger.info("File ${file.name} is not YAML, rejecting")
            return false
        }

        // Check if file is in .github/workflows directory
        val parent = file.parent
        if (parent?.name != "workflows") {
            logger.info("File ${file.name} is not in workflows directory (parent: ${parent?.name}), rejecting")
            return false
        }

        val grandParent = parent.parent
        if (grandParent?.name != ".github") {
            logger.info("File ${file.name} is not in .github/workflows directory (grandparent: ${grandParent?.name}), rejecting")
            return false
        }

        // Additional check: try to parse the file content to see if it's a valid workflow
        return try {
            val content = String(file.contentsToByteArray(), file.charset)
            // Simple check for workflow-specific keywords
            val isWorkflow = content.contains("\"on\":") || content.contains("on:") ||
                    content.contains("jobs:") || content.contains("\"jobs\":")

            logger.info("File ${file.name} workflow check result: $isWorkflow")
            isWorkflow
        } catch (e: Exception) {
            logger.warn("Error reading file ${file.name}: ${e.message}")
            false
        }
    }

    override fun createEditor(project: Project, file: VirtualFile): FileEditor {
        // Create text editor
        val textEditor = PsiAwareTextEditorProvider().createEditor(project, file) as TextEditor

        // Create preview editor
        val previewEditor = WorkflowPreviewEditor(file)

        // Create combined editor
        val combinedEditor = WorkflowTextEditorWithPreview(textEditor, previewEditor)

        // Set up document listener to sync text changes with preview
        setupDocumentListener(textEditor, previewEditor, combinedEditor)

        // Initial preview update
        try {
            val content = String(file.contentsToByteArray(), file.charset)
            previewEditor.updatePreview(content)
        } catch (e: Exception) {
            // Handle file reading error
            previewEditor.updatePreview("")
        }

        return combinedEditor
    }

    private fun setupDocumentListener(
        textEditor: TextEditor,
        previewEditor: WorkflowPreviewEditor,
        combinedEditor: WorkflowTextEditorWithPreview
    ) {
        val document = textEditor.editor.document

        val documentListener = object : DocumentListener {
            override fun documentChanged(event: DocumentEvent) {
                // Update preview when document changes
                ApplicationManager.getApplication().invokeLater {
                    try {
                        previewEditor.updatePreview(document.text)
                    } catch (e: Exception) {
                        // Editor might be disposed, ignore
                    }
                }
            }
        }

        document.addDocumentListener(documentListener)

        // Clean up listener when editor is disposed
        Disposer.register(combinedEditor) {
            document.removeDocumentListener(documentListener)
        }
    }

    override fun getEditorTypeId(): String = EDITOR_TYPE_ID

    override fun getPolicy(): FileEditorPolicy = FileEditorPolicy.PLACE_BEFORE_DEFAULT_EDITOR

    override fun readState(sourceElement: Element, project: Project, file: VirtualFile): FileEditorState {
        return FileEditorState.INSTANCE
    }

    override fun writeState(state: FileEditorState, project: Project, targetElement: Element) {
        // No state to write
    }
}
