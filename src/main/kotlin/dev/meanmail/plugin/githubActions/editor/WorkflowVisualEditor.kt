package dev.meanmail.plugin.githubActions.editor

import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.Logger
import com.intellij.ui.jcef.JBCefApp
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefBrowserBase
import dev.meanmail.plugin.githubActions.html.WorkflowHtmlGenerator
import dev.meanmail.plugin.githubActions.parser.WorkflowParser
import java.awt.BorderLayout
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * Visual editor component for GitHub Actions workflows using HTML rendering
 */
class WorkflowVisualEditor() : JPanel(BorderLayout()), Disposable {
    private val parser = WorkflowParser()
    private val htmlGenerator = WorkflowHtmlGenerator()
    private val browser: JBCefBrowser? = try {
        if (JBCefApp.isSupported()) {
            JBCefBrowser().apply {
                jbCefClient.setProperty(JBCefBrowserBase.Properties.NO_CONTEXT_MENU, true)
            }
        } else {
            null
        }
    } catch (e: Exception) {
        // JCEF not available
        null
    }

    init {
        setupUI()
    }

    private fun setupUI() {
        if (browser != null) {
            add(browser.component, BorderLayout.CENTER)
            // Load initial empty content
            updateContent("")
        } else {
            // Fallback for systems without JCEF support
            add(createFallbackComponent(), BorderLayout.CENTER)
        }
    }

    private fun createFallbackComponent(): JComponent {
        val panel = JPanel(BorderLayout())
        val label = javax.swing.JLabel(
            "<html><center><h2>HTML Preview not supported</h2>" +
                    "<p>Your system doesn't support embedded browser.</p>" +
                    "<p>Please use the text editor mode.</p></center></html>",
            javax.swing.SwingConstants.CENTER
        )
        panel.add(label, BorderLayout.CENTER)
        return panel
    }

    /**
     * Update the visual editor with new YAML content
     */
    fun updateContent(yamlContent: String) {
        if (browser == null) return

        val workflow = if (yamlContent.isBlank()) {
            null
        } else {
            parser.parse(yamlContent)
        }

        val html = htmlGenerator.generateHtml(workflow)
        browser.loadHTML(html)

        // SVG DAG is now embedded in HTML, no additional JavaScript needed
        if (workflow != null) {
            // Simple test to verify JavaScript execution works
            browser.cefBrowser.executeJavaScript(
                """
                setTimeout(function() {
                    console.log('SVG DAG loaded successfully');
                    const svg = document.getElementById('dag-svg');
                    if (svg) {
                        console.log('SVG element found:', svg);
                    } else {
                        console.log('SVG element not found');
                    }
                }, 500);
            """.trimIndent(), null, 0
            )
        }
    }

    /**
     * Execute JavaScript in the browser
     */
    fun executeJavaScript(jsCode: String) {
        val cefBrowser = browser?.cefBrowser
        if (cefBrowser == null) {
            return
        }

        try {
            cefBrowser.executeJavaScript(jsCode, null, 0)
        } catch (e: Exception) {
            LOG.error(e)
        }
    }

    override fun dispose() {
        browser?.dispose()
    }

    companion object {
        private val LOG = Logger.getInstance(WorkflowVisualEditor::class.java)
    }
}
