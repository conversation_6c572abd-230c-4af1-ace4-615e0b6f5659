package dev.meanmail.plugin.githubActions.editor

import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.util.UserDataHolderBase
import com.intellij.openapi.vfs.VirtualFile
import java.beans.PropertyChangeListener
import javax.swing.JComponent

/**
 * Text editor with preview for GitHub Actions workflow files
 * Provides split view with YAML text editor and visual workflow representation
 */
class WorkflowTextEditorWithPreview(
    textEditor: TextEditor,
    previewEditor: FileEditor,
    editorName: String = "GitHub Actions Workflow"
) : TextEditorWithPreview(textEditor, previewEditor, editorName, Layout.SHOW_EDITOR_AND_PREVIEW) {

    // No toolbar needed - controls are in HTML panel

    override fun getPreferredFocusedComponent(): JComponent? {
        return textEditor.preferredFocusedComponent
    }

    override fun getName(): String = "GitHub Actions Workflow"

    /**
     * Execute JavaScript in the preview editor
     */
    fun executeJavaScript(jsCode: String) {
        val preview = previewEditor
        if (preview is WorkflowPreviewEditor) {
            preview.executeJavaScript(jsCode)
        }
    }
}

/**
 * Preview editor that displays the visual representation of the workflow
 */
class WorkflowPreviewEditor(
    private val file: VirtualFile
) : UserDataHolderBase(), FileEditor {

    private val visualEditor = WorkflowVisualEditor()

    override fun getComponent(): JComponent = visualEditor

    override fun getPreferredFocusedComponent(): JComponent? = visualEditor

    override fun getName(): String = "Workflow Preview"

    override fun setState(state: FileEditorState) {
        // No state to set for preview
    }

    override fun isModified(): Boolean = false

    override fun isValid(): Boolean = file.isValid

    override fun addPropertyChangeListener(listener: PropertyChangeListener) {
        // No properties to listen to
    }

    override fun removePropertyChangeListener(listener: PropertyChangeListener) {
        // No properties to listen to
    }

    override fun getCurrentLocation(): FileEditorLocation? = null

    /**
     * Execute JavaScript in the visual editor
     */
    fun executeJavaScript(jsCode: String) {
        visualEditor.executeJavaScript(jsCode)
    }

    override fun dispose() {
        visualEditor.dispose()
    }

    override fun getFile(): VirtualFile = file

    /**
     * Update the preview with new content from the text editor
     */
    fun updatePreview(content: String) {
        visualEditor.updateContent(content)
    }
}
