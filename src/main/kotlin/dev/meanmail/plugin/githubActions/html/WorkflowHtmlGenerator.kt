package dev.meanmail.plugin.githubActions.html

import dev.meanmail.plugin.githubActions.model.*

class WorkflowHtmlGenerator {

    fun generateHtml(workflow: WorkflowModel?): String {
        if (workflow == null) {
            return generateErrorHtml("Invalid or empty workflow file")
        }

        return buildString {
            append("""
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>GitHub Actions Workflow: ${workflow.name}</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 0;
                            background: #0d1117;
                            color: #f0f6fc;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            overflow: hidden;
                        }
                        #debug-info {
                            position: fixed;
                            top: 10px;
                            left: 10px;
                            background: rgba(0,0,0,0.8);
                            color: white;
                            padding: 10px;
                            border-radius: 5px;
                            font-family: monospace;
                            font-size: 12px;
                            max-width: 400px;
                            z-index: 1000;
                        }
                        #dag-container {
                            width: 100vw;
                            height: 100vh;
                            overflow: hidden;
                            position: absolute;
                            top: 0;
                            left: 0;
                        }
                        #dag-svg {
                            width: 100%;
                            height: 100%;
                            background: transparent;
                        }
                    </style>
                </head>
                <body>
                    <!-- Debug info panel -->
                    <div id="debug-info">
                        <div>Debug Info:</div>
                        <div id="debug-content">Loading...</div>
                    </div>

                    <div id="dag-container">
                        <svg id="dag-svg"></svg>
                    </div>

                    <!-- D3.js Library -->
                    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
                    <!-- SVG Pan Zoom Library -->
                    <script src="https://cdn.jsdelivr.net/npm/svg-pan-zoom@3.6.1/dist/svg-pan-zoom.min.js"></script>
                    <!-- D3-DAG Library (UMD version) -->
                    <script src="https://unpkg.com/d3-dag@0.11.5/dist/d3-dag.umd.js"></script>

                    <script>
                        // Simple debug system
                        let debugMessages = [];
                        
                        function addDebugMessage(message) {
                            debugMessages.push(new Date().toLocaleTimeString() + ': ' + message);
                            const debugContent = document.getElementById('debug-content');
                            if (debugContent) {
                                debugContent.innerHTML = debugMessages.slice(-8).join('<br/>');
                            }
                        }
                        
                        addDebugMessage('JavaScript works!');
                        addDebugMessage('D3 available: ' + (typeof d3 !== 'undefined'));
                        
                        // Test workflow data
                        const workflowData = ${generateD3WorkflowData(workflow)};
                        addDebugMessage('Workflow data: ' + (workflowData ? workflowData.length + ' items' : 'null'));
                        
                        if (workflowData && workflowData[0]) {
                            addDebugMessage('First item: ' + workflowData[0].id);
                        }

                        // Wait for libraries to load
                        setTimeout(function() {
                            addDebugMessage('Checking libraries after delay...');
                            addDebugMessage('d3.dagStratify: ' + (typeof d3.dagStratify !== 'undefined'));
                            addDebugMessage('d3.sugiyama: ' + (typeof d3.sugiyama !== 'undefined'));
                            
                            if (typeof d3.dagStratify !== 'undefined') {
                                addDebugMessage('SUCCESS: d3-dag loaded!');
                                createSimpleDAG();
                            } else {
                                addDebugMessage('ERROR: d3-dag not loaded');
                            }
                        }, 1000);
                        
                        function createSimpleDAG() {
                            try {
                                addDebugMessage('Creating DAG...');
                                
                                // Create DAG
                                const stratify = d3.dagStratify();
                                const dag = stratify(workflowData);
                                
                                addDebugMessage('DAG created with ' + dag.descendants().length + ' nodes');
                                
                                // Apply layout
                                const layout = d3.sugiyama().size([800, 600]);
                                layout(dag);
                                
                                addDebugMessage('Layout applied successfully');
                                
                                // Simple rendering
                                renderSimpleDAG(dag);
                                
                            } catch (error) {
                                addDebugMessage('ERROR: ' + error.message);
                            }
                        }
                        
                        function renderSimpleDAG(dag) {
                            addDebugMessage('Rendering DAG...');
                            
                            const svg = d3.select('#dag-svg');
                            const container = svg.append('g').attr('transform', 'translate(50, 50)');
                            
                            // Draw simple nodes
                            const nodes = container.selectAll('g')
                                .data(dag.descendants())
                                .enter().append('g')
                                .attr('transform', d => `translate(${'$'}{d.x},${'$'}{d.y})`);
                            
                            nodes.append('rect')
                                .attr('width', 100)
                                .attr('height', 50)
                                .attr('x', -50)
                                .attr('y', -25)
                                .attr('fill', '#1f6feb')
                                .attr('stroke', '#fff')
                                .attr('rx', 5);
                            
                            nodes.append('text')
                                .attr('text-anchor', 'middle')
                                .attr('dy', '0.35em')
                                .attr('fill', 'white')
                                .attr('font-size', '12px')
                                .text(d => d.data.id);
                            
                            // Draw simple links
                            container.selectAll('line')
                                .data(dag.links())
                                .enter().append('line')
                                .attr('x1', d => d.source.x)
                                .attr('y1', d => d.source.y)
                                .attr('x2', d => d.target.x)
                                .attr('y2', d => d.target.y)
                                .attr('stroke', '#58a6ff')
                                .attr('stroke-width', 2);
                            
                            addDebugMessage('DAG rendered successfully!');
                        }
                    </script>
                </body>
                </html>
            """.trimIndent())
        }
    }

    private fun generateErrorHtml(message: String): String {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Error</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        background: #0d1117;
                        color: #f85149;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100vh;
                    }
                    .error-container {
                        text-align: center;
                        max-width: 500px;
                    }
                    h1 {
                        font-size: 24px;
                        margin-bottom: 16px;
                    }
                    p {
                        font-size: 16px;
                        line-height: 1.5;
                        color: #8b949e;
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <h1>⚠️ Error</h1>
                    <p>$message</p>
                </div>
            </body>
            </html>
        """.trimIndent()
    }

    private fun generateD3WorkflowData(workflow: WorkflowModel): String {
        val dagData = mutableListOf<Map<String, Any>>()

        // Add trigger node
        val triggersList = getTriggersList(workflow.on)
        dagData.add(
            mapOf(
                "id" to "trigger",
                "parentIds" to emptyList<String>(),
                "label" to "🚀 Triggers\\n$triggersList",
                "color" to "#238636",
                "width" to 200,
                "height" to 80
            )
        )

        // Add job nodes in d3-dag format
        workflow.jobs.forEach { (jobId, job) ->
            val icon = if (job.uses != null) "🔄" else "⚙️"
            val jobName = escapeForJson(job.name ?: jobId)
            val runsOn = escapeForJson(job.runsOn.joinToString(", "))
            val stepCount = job.steps.size

            val color = if (job.uses != null) "#8957e5" else "#1f6feb"

            // d3-dag uses parentIds instead of links
            val parentIds = if (job.needs.isEmpty()) {
                listOf("trigger")
            } else {
                job.needs
            }

            dagData.add(
                mapOf(
                    "id" to jobId,
                    "parentIds" to parentIds,
                    "label" to "$icon $jobName\\n$runsOn\\n$stepCount steps",
                    "color" to color,
                    "width" to 200,
                    "height" to 80
                )
            )
        }

        // Convert to JSON for d3-dag
        val dagJson = dagData.joinToString(",\n") { node ->
            val parentIdsJson = (node["parentIds"] as List<*>).joinToString(", ") { "\"$it\"" }
            val otherEntries = node.entries.filter { it.key != "parentIds" }.joinToString(", ") { (key, value) ->
                when (value) {
                    is String -> "\"$key\": \"$value\""
                    is Number -> "\"$key\": $value"
                    else -> "\"$key\": \"$value\""
                }
            }
            "    {$otherEntries, \"parentIds\": [$parentIdsJson]}"
        }

        return """
        [
        $dagJson
        ]
        """.trimIndent()
    }

    private fun getTriggersList(triggers: WorkflowTriggers): String {
        val triggerList = mutableListOf<String>()
        
        if (triggers.push != null) triggerList.add("Push")
        if (triggers.pullRequest != null) triggerList.add("PR")
        if (triggers.schedule != null) triggerList.add("Schedule")
        if (triggers.workflowDispatch != null) triggerList.add("Manual")
        if (triggers.workflowCall != null) triggerList.add("Call")
        
        return if (triggerList.isEmpty()) "Manual" else triggerList.joinToString(", ")
    }

    private fun escapeForJson(text: String): String {
        return text
            .replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t")
    }
}
