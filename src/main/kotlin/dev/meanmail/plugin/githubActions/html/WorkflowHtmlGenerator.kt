package dev.meanmail.plugin.githubActions.html

import dev.meanmail.plugin.githubActions.model.WorkflowModel
import dev.meanmail.plugin.githubActions.model.WorkflowTriggers

/**
 * Generates HTML representation of GitHub Actions workflow
 */
class WorkflowHtmlGenerator {

    fun generateHtml(workflow: WorkflowModel?): String {
        if (workflow == null) {
            return generateErrorHtml("Invalid or empty workflow file")
        }

        return buildString {
            append(generateHtmlHeader())
            append(generateWorkflowContent(workflow))
            append(generateHtmlFooter())
        }
    }

    private fun generateHtmlHeader(): String = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>GitHub Actions Workflow</title>
            <style>
                ${generateCSS()}
            </style>
        </head>
        <body>
    """.trimIndent()

    private fun generateHtmlFooter(): String = """
        </body>
        </html>
    """.trimIndent()

    private fun generateCSS(): String = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1117;
            color: #f0f6fc;
            line-height: 1.5;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .workflow-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: #0d1117;
            overflow: hidden;
        }

        .workflow-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(33, 38, 44, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 16px 24px;
            z-index: 100;
        }

        .workflow-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            color: #58a6ff;
        }

        .workflow-subtitle {
            font-size: 14px;
            color: #8b949e;
            margin-top: 4px;
        }

        #dag-canvas {
            width: 100%;
            height: 100%;
            cursor: grab;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        #dag-canvas:active {
            cursor: grabbing;
        }

        .controls-info {
            position: absolute;
            top: 80px;
            right: 20px;
            background: rgba(33, 38, 44, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            color: #8b949e;
            z-index: 101;
        }

        .controls-info h4 {
            color: #f0f6fc;
            margin: 0 0 8px 0;
            font-size: 13px;
        }

        .controls-info div {
            margin: 4px 0;
        }

        .control-button {
            background: #21262d;
            border: 1px solid #30363d;
            color: #f0f6fc;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            margin: 2px;
            transition: background 0.2s;
        }

        .control-button:hover {
            background: #30363d;
        }

        .error-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #f85149;
            background: rgba(33, 38, 44, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 40px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 18px;
            font-weight: 500;
        }
    """.trimIndent()

    private fun generateErrorHtml(message: String): String = """
        ${generateHtmlHeader()}
        <div class="workflow-container">
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-message">$message</div>
            </div>
        </div>
        ${generateHtmlFooter()}
    """.trimIndent()

    private fun generateWorkflowContent(workflow: WorkflowModel): String = buildString {
        append("<div class=\"workflow-container\">")

        // Header
        append("<div class=\"workflow-header\">")
        append("<h1 class=\"workflow-title\">")
        append(workflow.name?.let { escapeHtml(it) } ?: "GitHub Actions Workflow")
        append("</h1>")
        append("</div>")

        // Controls info panel
        append(
            """
            <div class="controls-info">
                <h4>🎮 Interactive Controls</h4>
                <div>🖱️ Drag to pan</div>
                <div>🔍 Wheel to zoom</div>
                <div>⌨️ Keys: R=reset, C=center, +/-=zoom, F=fit</div>
                <div style="margin-top: 8px;">
                    <div>📏 Zoom: <span id="zoom-level">100%</span></div>
                    <div>📍 Pan: <span id="pan-info">(0, 0)</span></div>
                </div>
                <div style="margin-top: 8px;">
                    <div>📊 Jobs: ${workflow.jobs.size}</div>
                    <div>🚀 Triggers: ${getTriggersList(workflow.on)}</div>
                </div>
            </div>
        """.trimIndent()
        )

        // D3.js DAG
        append(
            """
            <!-- Debug info panel -->
            <div id="debug-info" style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-width: 400px; z-index: 1000;">
                <div>Debug Info:</div>
                <div id="debug-content">Loading...</div>
            </div>

            <div id="dag-container" style="width: 100vw; height: 100vh; overflow: hidden; position: absolute; top: 0; left: 0;">
                <svg id="dag-svg" style="width: 100%; height: 100%; background: transparent;"></svg>
            </div>

            <!-- D3.js Library -->
            <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"
                    onerror="console.error('Failed to load D3.js library')"></script>
            <!-- D3-DAG Library for automatic graph layout -->
            <script src="https://unpkg.com/d3-dag@0.11.5/dist/d3-dag.min.js"
                    onerror="console.error('Failed to load d3-dag library')"></script>
            <!-- SVG Pan Zoom Library -->
            <script src="https://cdn.jsdelivr.net/npm/svg-pan-zoom@3.6.1/dist/svg-pan-zoom.min.js"
                    onerror="console.error('Failed to load svg-pan-zoom library')"></script>
            <script>
                console.log('Initializing D3.js DAG with pan/zoom...');

                let panZoomInstance = null;
                let debugMessages = [];

                // Function to add debug messages to the page
                function addDebugMessage(message) {
                    debugMessages.push(new Date().toLocaleTimeString() + ': ' + message);
                    const debugContent = document.getElementById('debug-content');
                    if (debugContent) {
                        debugContent.innerHTML = debugMessages.slice(-10).join('<br/>');
                    }
                    console.log(message);
                }

                addDebugMessage('Script started');

                // Workflow data from Kotlin
                const workflowData = ${generateD3WorkflowData(workflow)};
                addDebugMessage('Workflow data loaded: ' + (workflowData ? workflowData.length + ' items' : 'null'));

                // Initialize D3-DAG
                document.addEventListener('DOMContentLoaded', function() {
                    addDebugMessage('DOM loaded, starting DAG initialization...');
                    addDebugMessage('D3 available: ' + (typeof d3 !== 'undefined'));
                    addDebugMessage('D3-DAG methods: ' + Object.keys(d3).filter(k => k.includes('dag')).join(', '));
                    addDebugMessage('Workflow data type: ' + typeof workflowData);
                    addDebugMessage('Workflow data length: ' + (workflowData ? workflowData.length : 'undefined'));
                    if (workflowData && workflowData[0]) {
                        addDebugMessage('First item ID: ' + workflowData[0].id);
                    }

                    const svg = d3.select('#dag-svg');
                    console.log('SVG element found:', svg.node());

                    const container = svg.append('g');

                    const width = window.innerWidth;
                    const height = window.innerHeight;
                    console.log('Canvas size:', width, 'x', height);

                    console.log('Starting DAG creation...');

                    let dag;

                    // Check if d3-dag is available
                    addDebugMessage('Checking d3-dag availability...');
                    addDebugMessage('d3.dagStratify: ' + typeof d3.dagStratify);
                    addDebugMessage('d3.sugiyama: ' + typeof d3.sugiyama);

                    // Check for global d3dag object as alternative
                    addDebugMessage('window.d3dag: ' + typeof window.d3dag);
                    addDebugMessage('All d3 keys: ' + Object.keys(d3).slice(0, 10).join(', ') + '...');

                    if (typeof d3.dagStratify === 'undefined' && typeof window.d3dag === 'undefined') {
                        addDebugMessage('ERROR: d3-dag library not loaded properly');
                        addDebugMessage('Available d3 properties: ' + Object.keys(d3).filter(k => k.includes('dag')).join(', '));

                        // Try to wait a bit more for the library to load
                        setTimeout(() => {
                            addDebugMessage('Retrying after delay...');
                            addDebugMessage('d3.dagStratify after delay: ' + typeof d3.dagStratify);
                            if (typeof d3.dagStratify !== 'undefined') {
                                addDebugMessage('Library loaded after delay, retrying...');
                                // Retry the DAG creation
                                location.reload();
                            }
                        }, 2000);
                        return;
                    }

                    try {
                        addDebugMessage('d3-dag is available, creating DAG...');

                        // Create DAG using d3-dag
                        const stratify = d3.dagStratify();
                        dag = stratify(workflowData);

                        addDebugMessage('DAG created successfully');
                        addDebugMessage('DAG nodes count: ' + dag.descendants().length);

                        // Configure Sugiyama layout for horizontal layout (GitHub-style)
                        const layout = d3.sugiyama()
                            .size([width - 200, height - 200])
                            .layering(d3.layeringLongestPath())
                            .decross(d3.decrossOpt())
                            .coord(d3.coordSimplex())
                            .nodeSize([220, 100]); // Set consistent node spacing

                        // Apply layout
                        layout(dag);

                        addDebugMessage('DAG layout completed');
                        if (dag.descendants()[0]) {
                            addDebugMessage('First node position: (' + dag.descendants()[0].x + ', ' + dag.descendants()[0].y + ')');
                        }
                    } catch (error) {
                        addDebugMessage('ERROR creating DAG: ' + error.message);
                        console.error('Error creating DAG:', error);
                        return;
                    }

                    // Create links using d3-dag layout
                    const link = container.append('g')
                        .selectAll('path')
                        .data(dag.links())
                        .enter().append('path')
                        .attr('stroke', '#58a6ff')
                        .attr('stroke-width', 2)
                        .attr('fill', 'none')
                        .attr('marker-end', 'url(#arrowhead)')
                        .style('opacity', 0.8)
                        .attr('d', d => {
                            const source = d.source;
                            const target = d.target;

                            // Create curved path from source to target
                            const dx = target.x - source.x;
                            const dy = target.y - source.y;
                            const dr = Math.sqrt(dx * dx + dy * dy) * 0.3;

                            return `M${'$'}{source.x},${'$'}{source.y}A${'$'}{dr},${'$'}{dr} 0 0,1 ${'$'}{target.x},${'$'}{target.y}`;
                        });

                    // Create nodes using d3-dag layout
                    const node = container.append('g')
                        .selectAll('g')
                        .data(dag.descendants())
                        .enter().append('g')
                        .attr('class', 'node')
                        .attr('transform', d => `translate(${'$'}{d.x},${'$'}{d.y})`)
                        .style('opacity', 0);

                    // Add rectangles for nodes
                    node.append('rect')
                        .attr('width', d => d.data.width || 200)
                        .attr('height', d => d.data.height || 80)
                        .attr('x', d => -(d.data.width || 200) / 2)
                        .attr('y', d => -(d.data.height || 80) / 2)
                        .attr('rx', 8)
                        .attr('fill', d => d.data.color || '#1f6feb')
                        .attr('stroke', '#30363d')
                        .attr('stroke-width', 2)
                        .on('mouseover', function(event, d) {
                            // Add hover effect
                            d3.select(this)
                                .attr('stroke-width', 3)
                                .attr('filter', 'brightness(1.1)');

                            // Show tooltip
                            showTooltip(event, d);
                        })
                        .on('mouseout', function(event, d) {
                            // Remove hover effect
                            d3.select(this)
                                .attr('stroke-width', 2)
                                .attr('filter', 'none');

                            // Hide tooltip
                            hideTooltip();
                        });

                    // Add text to nodes
                    node.each(function(d) {
                        const nodeGroup = d3.select(this);
                        const lines = d.data.label.split('\\n');

                        lines.forEach((line, i) => {
                            const text = nodeGroup.append('text')
                                .attr('text-anchor', 'middle')
                                .attr('dy', (i - lines.length/2 + 0.5) * 16)
                                .attr('fill', '#f0f6fc')
                                .style('font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif')
                                .text(line);

                            // Style first line as title
                            if (i === 0) {
                                text.style('font-weight', '600')
                                    .style('font-size', '14px');
                            } else {
                                text.style('font-size', '11px')
                                    .style('opacity', '0.8');
                            }
                        });
                    });

                    // Create arrow marker
                    svg.append('defs').append('marker')
                        .attr('id', 'arrowhead')
                        .attr('viewBox', '0 -5 10 10')
                        .attr('refX', 8)
                        .attr('refY', 0)
                        .attr('markerWidth', 6)
                        .attr('markerHeight', 6)
                        .attr('orient', 'auto')
                        .append('path')
                        .attr('d', 'M0,-5L10,0L0,5')
                        .attr('fill', '#58a6ff');

                    // Animate nodes appearance
                    node.transition()
                        .duration(800)
                        .delay((d, i) => i * 100)
                        .style('opacity', 1);

                    // Animate links appearance
                    link.style('opacity', 0)
                        .transition()
                        .duration(600)
                        .delay(200)
                        .style('opacity', 0.8);

                    addDebugMessage('DAG rendering completed successfully!');

                    // Initialize pan/zoom immediately
                    setTimeout(() => {
                        panZoomInstance = svgPanZoom('#dag-svg', {
                            zoomEnabled: true,
                            controlIconsEnabled: false,
                            fit: false,  // We'll call fit manually with padding
                            center: false, // We'll call center manually
                            contain: false,
                            minZoom: 0.1,
                            maxZoom: 10,
                            zoomScaleSensitivity: 0.2,
                            dblClickZoomEnabled: true,
                            mouseWheelZoomEnabled: true,
                            preventMouseEventsDefault: true,
                            beforeZoom: function(oldScale, newScale) {
                                const zoomLevel = document.getElementById('zoom-level');
                                if (zoomLevel) {
                                    zoomLevel.textContent = (newScale * 100).toFixed(0) + '%';
                                }
                                return true;
                            },
                            beforePan: function(oldPan, newPan) {
                                const panInfo = document.getElementById('pan-info');
                                if (panInfo) {
                                    panInfo.textContent = '(' + newPan.x.toFixed(0) + ', ' + newPan.y.toFixed(0) + ')';
                                }
                                return true;
                            }
                        });

                        // Manually fit and center with padding
                        setTimeout(() => {
                            if (panZoomInstance) {
                                // Calculate bounding box of all nodes from DAG
                                let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                                dag.descendants().forEach(node => {
                                    const nodeWidth = node.data.width || 200;
                                    const nodeHeight = node.data.height || 80;
                                    minX = Math.min(minX, node.x - nodeWidth/2);
                                    maxX = Math.max(maxX, node.x + nodeWidth/2);
                                    minY = Math.min(minY, node.y - nodeHeight/2);
                                    maxY = Math.max(maxY, node.y + nodeHeight/2);
                                });

                                // Add padding
                                const padding = 50;
                                const contentWidth = maxX - minX + 2 * padding;
                                const contentHeight = maxY - minY + 2 * padding;
                                const contentCenterX = (minX + maxX) / 2;
                                const contentCenterY = (minY + maxY) / 2;

                                // Calculate scale to fit with padding
                                const scaleX = width / contentWidth;
                                const scaleY = height / contentHeight;
                                const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%

                                // Apply zoom and pan
                                panZoomInstance.zoom(scale);
                                panZoomInstance.pan({
                                    x: width/2 - contentCenterX * scale,
                                    y: height/2 - contentCenterY * scale
                                });
                            }
                        }, 100);

                        console.log('Pan/zoom initialized and centered with padding');
                    }, 500); // Short delay for DOM to settle

                    console.log('D3.js DAG with pan/zoom initialized');
                });

                // Tooltip functions
                function showTooltip(event, d) {
                    const tooltip = d3.select('body').append('div')
                        .attr('class', 'dag-tooltip')
                        .style('opacity', 0);

                    let tooltipContent = `<strong>${'$'}{d.data.id}</strong><br/>`;

                    if (d.data.id === 'trigger') {
                        tooltipContent += `Type: Workflow Triggers<br/>`;
                        tooltipContent += `Events: ${'$'}{d.data.label.split('\\n')[1]}`;
                    } else {
                        tooltipContent += `Type: Job<br/>`;
                        const labelParts = d.data.label.split('\\n');
                        if (labelParts.length > 1) {
                            tooltipContent += `Runs on: ${'$'}{labelParts[1]}<br/>`;
                        }
                        if (labelParts.length > 2) {
                            tooltipContent += `Steps: ${'$'}{labelParts[2]}`;
                        }
                    }

                    tooltip.html(tooltipContent)
                        .style('left', (event.pageX + 10) + 'px')
                        .style('top', (event.pageY - 10) + 'px')
                        .transition()
                        .duration(200)
                        .style('opacity', 1);
                }

                function hideTooltip() {
                    d3.selectAll('.dag-tooltip')
                        .transition()
                        .duration(200)
                        .style('opacity', 0)
                        .remove();
                }

                // Control functions
                window.resetView = function() {
                    console.log('Reset view clicked');
                    if (panZoomInstance && dag) {
                        // Use the same centering logic as initial load
                        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                        dag.descendants().forEach(node => {
                            const nodeWidth = node.data.width || 200;
                            const nodeHeight = node.data.height || 80;
                            minX = Math.min(minX, node.x - nodeWidth/2);
                            maxX = Math.max(maxX, node.x + nodeWidth/2);
                            minY = Math.min(minY, node.y - nodeHeight/2);
                            maxY = Math.max(maxY, node.y + nodeHeight/2);
                        });

                        const padding = 50;
                        const contentWidth = maxX - minX + 2 * padding;
                        const contentHeight = maxY - minY + 2 * padding;
                        const contentCenterX = (minX + maxX) / 2;
                        const contentCenterY = (minY + maxY) / 2;

                        const scaleX = window.innerWidth / contentWidth;
                        const scaleY = window.innerHeight / contentHeight;
                        const scale = Math.min(scaleX, scaleY, 1);

                        panZoomInstance.zoom(scale);
                        panZoomInstance.pan({
                            x: window.innerWidth/2 - contentCenterX * scale,
                            y: window.innerHeight/2 - contentCenterY * scale
                        });
                    }
                };

                window.centerView = function() {
                    console.log('Center view clicked');
                    if (panZoomInstance) {
                        panZoomInstance.center();
                    }
                };

                window.zoomIn = function() {
                    console.log('Zoom in clicked');
                    if (panZoomInstance) {
                        panZoomInstance.zoomIn();
                    }
                };

                window.zoomOut = function() {
                    console.log('Zoom out clicked');
                    if (panZoomInstance) {
                        panZoomInstance.zoomOut();
                    }
                };

                window.fitToWindow = function() {
                    console.log('Fit to window clicked');
                    if (panZoomInstance) {
                        panZoomInstance.fit();
                        panZoomInstance.center();
                    }
                };

                // Keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

                    switch(e.key) {
                        case 'r':
                        case 'R':
                            window.resetView();
                            break;
                        case 'c':
                        case 'C':
                            window.centerView();
                            break;
                        case '+':
                        case '=':
                            window.zoomIn();
                            break;
                        case '-':
                            window.zoomOut();
                            break;
                        case '0':
                            window.resetView();
                            break;
                        case 'f':
                        case 'F':
                            window.fitToWindow();
                            break;
                    }
                });

                console.log('D3.js DAG with pan/zoom initialized');
                console.log('Keyboard shortcuts: R=reset, C=center, +/-=zoom, 0=reset, F=fit');
            </script>

            <!-- Additional CSS for D3 styling -->
            <style>
                .node {
                    cursor: pointer;
                }
                .node rect {
                    transition: all 0.2s ease;
                }
                .node:hover rect {
                    stroke-width: 3;
                    filter: brightness(1.1);
                }
                .node text {
                    pointer-events: none;
                    user-select: none;
                }

                /* Tooltip styles */
                .dag-tooltip {
                    position: absolute;
                    background: rgba(33, 38, 44, 0.95);
                    border: 1px solid #30363d;
                    border-radius: 8px;
                    padding: 12px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 12px;
                    color: #f0f6fc;
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
                    backdrop-filter: blur(10px);
                    pointer-events: none;
                    z-index: 1000;
                    max-width: 250px;
                    line-height: 1.4;
                }

                .dag-tooltip strong {
                    color: #58a6ff;
                    font-weight: 600;
                }

                /* Link hover effects */
                path {
                    transition: all 0.2s ease;
                }

                path:hover {
                    stroke: #79c0ff !important;
                    stroke-width: 3 !important;
                    opacity: 1 !important;
                }
            </style>
        """.trimIndent()
        )

        append("</div>")
    }

    /**
     * Generate D3.js data structure for the workflow
     */
    private fun generateD3WorkflowData(workflow: WorkflowModel): String {
        val dagData = mutableListOf<Map<String, Any>>()

        // Add trigger node
        val triggersList = getTriggersList(workflow.on)
        dagData.add(
            mapOf(
                "id" to "trigger",
                "parentIds" to emptyList<String>(),
                "label" to "🚀 Triggers\\n$triggersList",
                "color" to "#238636",
                "width" to 200,
                "height" to 80
            )
        )

        // Add job nodes in d3-dag format
        workflow.jobs.forEach { (jobId, job) ->
            val icon = if (job.uses != null) "🔄" else "⚙️"
            val jobName = escapeForJson(job.name ?: jobId)
            val runsOn = escapeForJson(job.runsOn.joinToString(", "))
            val stepCount = job.steps.size

            val color = if (job.uses != null) "#8957e5" else "#1f6feb"

            // d3-dag uses parentIds instead of links
            val parentIds = if (job.needs.isEmpty()) {
                listOf("trigger")
            } else {
                job.needs
            }

            dagData.add(
                mapOf(
                    "id" to jobId,
                    "parentIds" to parentIds,
                    "label" to "$icon $jobName\\n$runsOn\\n$stepCount steps",
                    "color" to color,
                    "width" to 200,
                    "height" to 80
                )
            )
        }

        // Convert to JSON for d3-dag
        val dagJson = dagData.joinToString(",\n") { node ->
            val parentIdsJson = (node["parentIds"] as List<*>).joinToString(", ") { "\"$it\"" }
            val otherEntries = node.entries.filter { it.key != "parentIds" }.joinToString(", ") { (key, value) ->
                when (value) {
                    is String -> "\"$key\": \"$value\""
                    is Number -> "\"$key\": $value"
                    else -> "\"$key\": \"$value\""
                }
            }
            "    {$otherEntries, \"parentIds\": [$parentIdsJson]}"
        }

        return """
        [
        $dagJson
        ]
        """.trimIndent()
    }

    /**
     * Generate Mermaid flowchart code for the workflow (legacy)
     */
    private fun generateMermaidDAG(workflow: WorkflowModel): String = buildString {
        appendLine("flowchart LR")

        // Create trigger node
        val triggersList = getTriggersList(workflow.on)
        appendLine("    trigger[\"<b>🚀 Triggers</b><br/><small>$triggersList</small>\"]")

        // Calculate job levels for proper ordering
        val jobLevels = mutableMapOf<String, Int>()
        val visited = mutableSetOf<String>()

        fun calculateLevel(jobId: String): Int {
            if (visited.contains(jobId)) return jobLevels[jobId] ?: 0
            visited.add(jobId)

            val job = workflow.jobs[jobId]
            if (job == null || job.needs.isEmpty()) {
                jobLevels[jobId] = 1
                return 1
            }

            val maxDepLevel = job.needs.maxOfOrNull { calculateLevel(it) } ?: 0
            val level = maxDepLevel + 1
            jobLevels[jobId] = level
            return level
        }

        workflow.jobs.keys.forEach { calculateLevel(it) }

        // Generate job nodes with styled text
        workflow.jobs.forEach { (jobId, job) ->
            val icon = if (job.uses != null) "🔄" else "⚙️"
            val jobName = escapeForMermaid(job.name ?: jobId)
            val runsOn = escapeForMermaid(job.runsOn.joinToString(", "))
            val stepCount = job.steps.size

            // Use HTML styling for different text sizes
            val nodeContent = "<b>$icon $jobName</b><br/><small>$runsOn</small><br/><small>$stepCount steps</small>"
            appendLine("    $jobId[\"$nodeContent\"]")
        }

        // Generate connections
        workflow.jobs.forEach { (jobId, job) ->
            if (job.needs.isEmpty()) {
                // Connect trigger to jobs with no dependencies
                appendLine("    trigger --> $jobId")
            } else {
                // Connect dependency jobs
                job.needs.forEach { depJobId ->
                    appendLine("    $depJobId --> $jobId")
                }
            }
        }

        // Add styling
        appendLine("    classDef triggerClass fill:#238636,stroke:#30363d,stroke-width:2px,color:#f0f6fc")
        appendLine("    classDef jobClass fill:#1f6feb,stroke:#30363d,stroke-width:2px,color:#f0f6fc")
        appendLine("    classDef reusableClass fill:#8957e5,stroke:#30363d,stroke-width:2px,color:#f0f6fc")

        appendLine("    class trigger triggerClass")
        workflow.jobs.forEach { (jobId, job) ->
            val className = if (job.uses != null) "reusableClass" else "jobClass"
            appendLine("    class $jobId $className")
        }
    }

    /**
     * Generate interactive SVG DAG with pan and zoom support (legacy)
     */
    private fun generateInteractiveSVGDAG(workflow: WorkflowModel): String = buildString {
        val nodeWidth = 200
        val nodeHeight = 80
        val nodeSpacing = 250
        val levelSpacing = 150

        // Calculate job levels
        val jobLevels = mutableMapOf<String, Int>()
        val visited = mutableSetOf<String>()

        fun calculateLevel(jobId: String): Int {
            if (visited.contains(jobId)) return jobLevels[jobId] ?: 0
            visited.add(jobId)

            val job = workflow.jobs[jobId]
            if (job == null || job.needs.isEmpty()) {
                jobLevels[jobId] = 1
                return 1
            }

            val maxDepLevel = job.needs.maxOfOrNull { calculateLevel(it) } ?: 0
            val level = maxDepLevel + 1
            jobLevels[jobId] = level
            return level
        }

        workflow.jobs.keys.forEach { calculateLevel(it) }

        // Group jobs by level
        val levelGroups = jobLevels.entries.groupBy { it.value }.mapValues { entry ->
            entry.value.map { it.key }
        }

        val maxLevel = levelGroups.keys.maxOrNull() ?: 1
        val totalWidth = 100 + (maxLevel + 1) * nodeSpacing
        val totalHeight = 200 + (levelGroups.values.maxOfOrNull { it.size } ?: 1) * (nodeHeight + 40)

        append("""<svg id="dag-svg" width="100%" height="100%" viewBox="0 0 $totalWidth $totalHeight" style="background: #0d1117; cursor: grab;">""")

        // Add gradients and arrow definitions
        append(
            """
            <defs>
                <linearGradient id="triggerGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#2ea043;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#238636;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="jobGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#388bfd;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#1f6feb;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="reusableGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#a475f9;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#8957e5;stop-opacity:1" />
                </linearGradient>

                <!-- Arrow gradient -->
                <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#6e7681;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:#30363d;stop-opacity:1" />
                </linearGradient>

                <!-- Arrow marker -->
                <marker id="arrowhead" markerWidth="10" markerHeight="10"
                        refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                    <path d="M0,0 L0,6 L9,3 z" fill="#58a6ff" stroke="none"/>
                </marker>

                <!-- Glow filter for hover effect -->
                <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>

            <style>
                .connection-path {
                    transition: all 0.2s ease;
                }
                .connection-path:hover {
                    stroke: #79c0ff !important;
                    stroke-width: 3 !important;
                    opacity: 1 !important;
                }
            </style>
        """.trimIndent()
        )

        // Draw trigger node
        val triggerX = 50
        val triggerY = 100

        append(
            """
            <rect x="$triggerX" y="$triggerY" width="$nodeWidth" height="$nodeHeight"
                  rx="8" fill="url(#triggerGrad)" stroke="#30363d" stroke-width="2"/>
            <text x="${triggerX + 15}" y="${triggerY + 25}" fill="#f0f6fc" font-family="Arial" font-size="14" font-weight="bold">
                🚀 Triggers
            </text>
            <text x="${triggerX + 15}" y="${triggerY + 45}" fill="#8b949e" font-family="Arial" font-size="12">
                ${getTriggersList(workflow.on)}
            </text>
        """.trimIndent()
        )

        // Draw job nodes and connections
        levelGroups.forEach { (level, jobIds) ->
            jobIds.forEachIndexed { index, jobId ->
                val job = workflow.jobs[jobId] ?: return@forEachIndexed
                val x = 50 + level * nodeSpacing
                val y = 100 + levelSpacing + index * (nodeHeight + 40)

                val fillGradient = if (job.uses != null) "url(#reusableGrad)" else "url(#jobGrad)"

                append(
                    """
                    <rect x="$x" y="$y" width="$nodeWidth" height="$nodeHeight"
                          rx="8" fill="$fillGradient" stroke="#30363d" stroke-width="2"/>
                    <text x="${x + 15}" y="${y + 25}" fill="#f0f6fc" font-family="Arial" font-size="14" font-weight="bold">
                        ${if (job.uses != null) "🔄" else "⚙️"} ${escapeHtml(job.name ?: jobId)}
                    </text>
                    <text x="${x + 15}" y="${y + 45}" fill="#8b949e" font-family="Arial" font-size="12">
                        ${escapeHtml(job.runsOn.joinToString(", "))}
                    </text>
                    <text x="${x + 15}" y="${y + 65}" fill="#8b949e" font-family="Arial" font-size="12">
                        ${job.steps.size} steps
                    </text>
                """.trimIndent()
                )

                // Draw connections
                if (job.needs.isEmpty() && level == 1) {
                    // Connect to trigger
                    val fromX = triggerX + nodeWidth
                    val fromY = triggerY + nodeHeight / 2
                    val toX = x
                    val toY = y + nodeHeight / 2

                    append(drawArrow(fromX, fromY, toX, toY, level))
                } else {
                    // Connect to dependency jobs
                    job.needs.forEach { depJobId ->
                        val depLevel = jobLevels[depJobId] ?: 0
                        val depIndex = levelGroups[depLevel]?.indexOf(depJobId) ?: 0
                        val depX = 50 + depLevel * nodeSpacing
                        val depY = 100 + levelSpacing + depIndex * (nodeHeight + 40)

                        val fromX = depX + nodeWidth
                        val fromY = depY + nodeHeight / 2
                        val toX = x
                        val toY = y + nodeHeight / 2

                        append(drawArrow(fromX, fromY, toX, toY, level - depLevel))
                    }
                }
            }
        }

        append("</svg>")
    }

    private fun generateSimpleSVGDAG(workflow: WorkflowModel): String = buildString {
        val nodeWidth = 200
        val nodeHeight = 80
        val nodeSpacing = 250
        val levelSpacing = 150

        // Calculate job levels
        val jobLevels = mutableMapOf<String, Int>()
        val visited = mutableSetOf<String>()

        fun calculateLevel(jobId: String): Int {
            if (visited.contains(jobId)) return jobLevels[jobId] ?: 0
            visited.add(jobId)

            val job = workflow.jobs[jobId]
            if (job == null || job.needs.isEmpty()) {
                jobLevels[jobId] = 1
                return 1
            }

            val maxDepLevel = job.needs.maxOfOrNull { calculateLevel(it) } ?: 0
            val level = maxDepLevel + 1
            jobLevels[jobId] = level
            return level
        }

        workflow.jobs.keys.forEach { calculateLevel(it) }

        // Group jobs by level
        val levelGroups = jobLevels.entries.groupBy { it.value }.mapValues { entry ->
            entry.value.map { it.key }
        }

        val maxLevel = levelGroups.keys.maxOrNull() ?: 1
        val totalWidth = 100 + (maxLevel + 1) * nodeSpacing
        val totalHeight = 200 + (levelGroups.values.maxOfOrNull { it.size } ?: 1) * (nodeHeight + 40)

        append("<svg width=\"$totalWidth\" height=\"$totalHeight\" style=\"background: #0d1117; margin-top: 80px;\">")

        // Define gradients and styles
        append(
            """
            <defs>
                <linearGradient id="triggerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#238636;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#196127;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="jobGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#1f6feb;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#0969da;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="reusableGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#8957e5;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#6f42c1;stop-opacity:1" />
                </linearGradient>
            </defs>
        """.trimIndent()
        )

        // Draw trigger node
        val triggerX = 50
        val triggerY = 100

        append(
            """
            <rect x="$triggerX" y="$triggerY" width="$nodeWidth" height="$nodeHeight"
                  rx="8" fill="url(#triggerGrad)" stroke="#30363d" stroke-width="2"/>
            <text x="${triggerX + 15}" y="${triggerY + 25}" fill="#f0f6fc" font-family="Arial" font-size="14" font-weight="bold">
                🚀 Triggers
            </text>
            <text x="${triggerX + 15}" y="${triggerY + 45}" fill="#8b949e" font-family="Arial" font-size="12">
                ${getTriggersList(workflow.on)}
            </text>
        """.trimIndent()
        )

        // Draw job nodes and connections
        levelGroups.forEach { (level, jobIds) ->
            jobIds.forEachIndexed { index, jobId ->
                val job = workflow.jobs[jobId] ?: return@forEachIndexed
                val x = 50 + level * nodeSpacing
                val y = 100 + levelSpacing + index * (nodeHeight + 40)

                val fillGradient = if (job.uses != null) "url(#reusableGrad)" else "url(#jobGrad)"

                // Draw job node
                append(
                    """
                    <rect x="$x" y="$y" width="$nodeWidth" height="$nodeHeight"
                          rx="8" fill="$fillGradient" stroke="#30363d" stroke-width="2"/>
                    <text x="${x + 15}" y="${y + 25}" fill="#f0f6fc" font-family="Arial" font-size="14" font-weight="bold">
                        ${if (job.uses != null) "🔄" else "⚙️"} ${escapeHtml(job.name ?: jobId)}
                    </text>
                    <text x="${x + 15}" y="${y + 45}" fill="#8b949e" font-family="Arial" font-size="12">
                        ${escapeHtml(job.runsOn.joinToString(", "))}
                    </text>
                    <text x="${x + 15}" y="${y + 60}" fill="#8b949e" font-family="Arial" font-size="11">
                        ${job.steps.size} steps${if (job.environment != null) " • ${job.environment.name}" else ""}
                    </text>
                """.trimIndent()
                )

                // Draw connections
                if (job.needs.isEmpty() && level == 1) {
                    // Connect to trigger
                    val fromX = triggerX + nodeWidth
                    val fromY = triggerY + nodeHeight / 2
                    val toX = x
                    val toY = y + nodeHeight / 2

                    append(drawArrow(fromX, fromY, toX, toY, level))
                } else {
                    // Connect to dependency jobs
                    job.needs.forEach { depJobId ->
                        val depLevel = jobLevels[depJobId] ?: 0
                        val depIndex = levelGroups[depLevel]?.indexOf(depJobId) ?: 0
                        val depX = 50 + depLevel * nodeSpacing
                        val depY = 100 + levelSpacing + depIndex * (nodeHeight + 40)

                        val fromX = depX + nodeWidth
                        val fromY = depY + nodeHeight / 2
                        val toX = x
                        val toY = y + nodeHeight / 2

                        append(drawArrow(fromX, fromY, toX, toY, level - depLevel))
                    }
                }
            }
        }

        append("</svg>")
    }

    private fun getTriggersList(triggers: WorkflowTriggers): String {
        val triggerNames = mutableListOf<String>()
        triggers.push?.let { triggerNames.add("Push") }
        triggers.pullRequest?.let { triggerNames.add("PR") }
        triggers.workflowDispatch?.let { triggerNames.add("Manual") }
        triggers.workflowCall?.let { triggerNames.add("Call") }
        if (triggers.schedule.isNotEmpty()) triggerNames.add("Schedule")
        triggers.other.keys.forEach { triggerNames.add(it) }

        return if (triggerNames.isEmpty()) "No triggers" else triggerNames.joinToString(", ")
    }

    private fun drawArrow(fromX: Int, fromY: Int, toX: Int, toY: Int, levelDiff: Int = 1): String {
        // Padding to avoid overlapping with node borders
        val adjustedToX = toX - 8
        val deltaX = adjustedToX - fromX
        val deltaY = toY - fromY

        // Calculate path based on level difference to avoid intersections
        return if (kotlin.math.abs(deltaY) < 20) {
            // Same level or close - use simple horizontal curve
            val controlDistance = (deltaX * 0.4).coerceAtLeast(40.0)
            val control1X = fromX + controlDistance
            val control2X = adjustedToX - controlDistance

            """
            <path d="M $fromX $fromY C $control1X $fromY $control2X $toY $adjustedToX $toY"
                  class="connection-path"
                  stroke="#58a6ff"
                  stroke-width="2"
                  fill="none"
                  marker-end="url(#arrowhead)"
                  opacity="0.8"/>
            """.trimIndent()
        } else {
            // Different levels - use path that goes around blocks
            val midX = fromX + deltaX / 2
            val offsetY = if (deltaY > 0) -30 else 30 // Go above or below
            val routeY = fromY + offsetY

            """
            <path d="M $fromX $fromY L ${fromX + 20} $fromY Q ${fromX + 40} $fromY ${fromX + 40} $routeY L $midX $routeY Q ${adjustedToX - 40} $routeY ${adjustedToX - 40} $toY Q ${adjustedToX - 20} $toY $adjustedToX $toY"
                  class="connection-path"
                  stroke="#58a6ff"
                  stroke-width="2"
                  fill="none"
                  marker-end="url(#arrowhead)"
                  opacity="0.8"/>
            """.trimIndent()
        }
    }

    private fun escapeHtml(text: String): String {
        return text
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;")
    }

    private fun escapeForMermaid(text: String): String {
        return text
            .replace("\"", "&quot;")
            .replace("[", "&#91;")
            .replace("]", "&#93;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
    }

    private fun escapeForJson(text: String): String {
        return text
            .replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t")
    }

}
