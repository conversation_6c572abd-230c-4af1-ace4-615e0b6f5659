package dev.meanmail.plugin.githubActions.model

/**
 * Data model representing a GitHub Actions workflow
 */
data class WorkflowModel(
    val name: String? = null,
    val on: WorkflowTriggers = WorkflowTriggers(),
    val env: Map<String, String> = emptyMap(),
    val defaults: WorkflowDefaults? = null,
    val concurrency: WorkflowConcurrency? = null,
    val jobs: Map<String, Job> = emptyMap()
)

/**
 * Workflow triggers (on section)
 */
data class WorkflowTriggers(
    val push: PushTrigger? = null,
    val pullRequest: PullRequestTrigger? = null,
    val schedule: List<ScheduleTrigger> = emptyList(),
    val workflowDispatch: WorkflowDispatchTrigger? = null,
    val workflowCall: WorkflowCallTrigger? = null,
    val other: Map<String, Any> = emptyMap()
)

data class PushTrigger(
    val branches: List<String> = emptyList(),
    val branchesIgnore: List<String> = emptyList(),
    val tags: List<String> = emptyList(),
    val tagsIgnore: List<String> = emptyList(),
    val paths: List<String> = emptyList(),
    val pathsIgnore: List<String> = emptyList()
)

data class PullRequestTrigger(
    val types: List<String> = emptyList(),
    val branches: List<String> = emptyList(),
    val branchesIgnore: List<String> = emptyList(),
    val paths: List<String> = emptyList(),
    val pathsIgnore: List<String> = emptyList()
)

data class ScheduleTrigger(
    val cron: String
)

data class WorkflowDispatchTrigger(
    val inputs: Map<String, WorkflowInput> = emptyMap()
)

data class WorkflowCallTrigger(
    val inputs: Map<String, WorkflowInput> = emptyMap(),
    val outputs: Map<String, WorkflowOutput> = emptyMap(),
    val secrets: Map<String, WorkflowSecret> = emptyMap()
)

data class WorkflowInput(
    val description: String? = null,
    val required: Boolean = false,
    val default: String? = null,
    val type: String = "string"
)

data class WorkflowOutput(
    val description: String? = null,
    val value: String
)

data class WorkflowSecret(
    val description: String? = null,
    val required: Boolean = false
)

/**
 * Workflow defaults
 */
data class WorkflowDefaults(
    val run: RunDefaults? = null
)

data class RunDefaults(
    val shell: String? = null,
    val workingDirectory: String? = null
)

/**
 * Workflow concurrency
 */
data class WorkflowConcurrency(
    val group: String,
    val cancelInProgress: Boolean = false
)

/**
 * Job definition
 */
data class Job(
    val name: String? = null,
    val runsOn: List<String> = emptyList(),
    val needs: List<String> = emptyList(),
    val if_: String? = null,
    val permissions: Map<String, String> = emptyMap(),
    val environment: JobEnvironment? = null,
    val concurrency: WorkflowConcurrency? = null,
    val outputs: Map<String, String> = emptyMap(),
    val env: Map<String, String> = emptyMap(),
    val defaults: WorkflowDefaults? = null,
    val timeoutMinutes: Int? = null,
    val strategy: JobStrategy? = null,
    val continueOnError: Boolean = false,
    val container: JobContainer? = null,
    val services: Map<String, JobService> = emptyMap(),
    val uses: String? = null,
    val with: Map<String, Any> = emptyMap(),
    val secrets: Map<String, String> = emptyMap(),
    val steps: List<Step> = emptyList()
)

data class JobEnvironment(
    val name: String,
    val url: String? = null
)

data class JobStrategy(
    val matrix: Map<String, Any> = emptyMap(),
    val failFast: Boolean = true,
    val maxParallel: Int? = null
)

data class JobContainer(
    val image: String,
    val credentials: Map<String, String> = emptyMap(),
    val env: Map<String, String> = emptyMap(),
    val ports: List<String> = emptyList(),
    val volumes: List<String> = emptyList(),
    val options: String? = null
)

data class JobService(
    val image: String,
    val credentials: Map<String, String> = emptyMap(),
    val env: Map<String, String> = emptyMap(),
    val ports: List<String> = emptyList(),
    val volumes: List<String> = emptyList(),
    val options: String? = null
)

/**
 * Step definition
 */
data class Step(
    val id: String? = null,
    val name: String? = null,
    val uses: String? = null,
    val run: String? = null,
    val with: Map<String, Any> = emptyMap(),
    val env: Map<String, String> = emptyMap(),
    val if_: String? = null,
    val continueOnError: Boolean = false,
    val timeoutMinutes: Int? = null,
    val shell: String? = null,
    val workingDirectory: String? = null
)
