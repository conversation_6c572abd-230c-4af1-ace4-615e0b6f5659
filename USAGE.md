# Как использовать плагин GitHub Actions для IntelliJ IDEA

## 🚀 Быстрый старт

### 1. Откройте workflow файл

1. Перейдите в папку `.github/workflows/` в вашем проекте
2. Откройте любой `.yml` или `.yaml` файл
3. <PERSON>л<PERSON>г<PERSON>н автоматически активируется для workflow файлов

### 2. Использование вкладок

После открытия workflow файла вы увидите **две вкладки** в нижней части редактора:

> 🎨 **Новая DAG-визуализация!** Теперь workflow отображается как интерактивный направленный граф (DAG) с возможностью
> панорамирования, масштабирования и hover-эффектами!

#### 📝 **Text** (Текстовая вкладка)

- Стандартный YAML редактор
- Подсветка синтаксиса
- Автодополнение
- Все возможности IntelliJ IDEA для YAML

#### 🎨 **Workflow Preview** (DAG-визуализация)

- **Интерактивный граф** workflow с узлами и связями
- **Темная тема** в стиле GitHub Dark
- **Панорамирование** - перетаскивайте граф мышью
- **Масштабирование** - используйте колесо мыши для zoom
- **Hover эффекты** - наведите на узел для подсветки связей
- **Автоматическое обновление** при изменении кода

### 3. Расположение вкладок

Вкладки находятся в **нижней части редактора**:

```
[Text] [Workflow Preview]
```

**Автоматическая синхронизация**: Изменения в текстовой вкладке автоматически отражаются в визуальной вкладке!

## 🎯 Что отображается в DAG-визуализации

### 🚀 Trigger Node (Узел триггеров)

- **Зеленый узел** в начале графа
- Показывает все триггеры: Push, Pull Request, Schedule, Manual Dispatch
- Стартовая точка для всех workflow

### ⚙️ Job Nodes (Узлы задач)

- **Синие узлы** для обычных jobs
- **Фиолетовые узлы** для reusable workflows
- Показывают:
    - Название job
    - Runner (ubuntu-latest, windows-latest, etc.)
    - Количество steps
    - Environment badge (если используется)

### 🔗 Dependencies (Зависимости)

- **Стрелки** показывают порядок выполнения
- **Автоматическое позиционирование** по уровням
- **Подсветка связей** при hover на узел

### 🎨 Интерактивность

- **Hover эффекты** - подсветка узла и связанных стрелок
- **Панорамирование** - перетаскивание графа
- **Масштабирование** - zoom колесом мыши
- **Адаптивная раскладка** - автоматическое позиционирование узлов

## 🎮 Управление DAG

### Навигация

- **Перетаскивание** - зажмите левую кнопку мыши и перетаскивайте граф
- **Масштабирование** - используйте колесо мыши для увеличения/уменьшения
- **Hover** - наведите курсор на узел для подсветки связей

### Цветовая схема

- 🚀 **Зеленый** - узел триггеров
- ⚙️ **Синий** - обычные jobs
- 🔄 **Фиолетовый** - reusable workflows
- 🔗 **Серый/Синий** - стрелки зависимостей

## 📝 Примеры использования

### Простой workflow

```yaml
name: Test Workflow
"on": push
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run tests
        run: npm test
```

### Сложный workflow с matrix

```yaml
name: Matrix Build
"on":
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [16, 18, 20]
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm test
```

## 🐛 Устранение неполадок

### Плагин не активируется

- Убедитесь, что файл находится в папке `.github/workflows/`
- Проверьте, что файл имеет расширение `.yml` или `.yaml`
- Перезапустите IntelliJ IDEA

### Не отображается визуальное представление

- Проверьте корректность YAML синтаксиса
- Убедитесь, что используете `"on":` вместо `on:` (в кавычках)
- Проверьте, что workflow содержит секцию `jobs`

### Кнопки переключения не видны

- Убедитесь, что открыт именно workflow файл
- Проверьте, что плагин установлен и активен
- Попробуйте закрыть и снова открыть файл

## 💡 Советы

1. **Используйте кавычки для "on"**: `"on":` вместо `on:` для корректного парсинга
2. **Проверяйте YAML синтаксис**: некорректный YAML не будет отображаться в визуальном режиме
3. **Используйте Split mode** для одновременного редактирования и просмотра
4. **Сохраняйте файл** для обновления визуального представления

## 🔗 Полезные ссылки

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Workflow Syntax](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [Actions Marketplace](https://github.com/marketplace?type=actions)
