# Gradle
gradleVersion=8.14.1
org.gradle.parallel=true
# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache=true
# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching=true
# https://plugins.jetbrains.com/docs/intellij/build-number-ranges.html#intellij-platform-based-products-of-recent-ide-versions
jvmVersion=21
#
# Plugin
group=dev.meanmail
pluginName=GitHub Actions
version=1.0.0
# https://plugins.jetbrains.com/docs/intellij/kotlin.html#kotlin-standard-library
kotlin.stdlib.default.dependency=false
#
# Platform
platformSinceBuild=243
# Develop platform
platformVersion=2024.3
platformType=PC
plugins=
platformBundledPlugins=
#
# Publish
publishChannel=Stable
