# GitHub Actions Plugin for IntelliJ IDEA

Плагин для IntelliJ IDEA, который добавляет поддержку визуального отображения GitHub Actions workflow файлов.

## Возможности

- **Визуальный редактор workflow**: Отображение workflow файлов в удобном графическом виде
- **Переключение между режимами**: Возможность переключения между текстовым и визуальным режимом редактирования
- **Автоматическая синхронизация**: Изменения в текстовом редакторе автоматически отражаются в визуальном представлении
- **Поддержка всех элементов workflow**: Jobs, steps, triggers, environment variables и другие компоненты

## Установка

### Из исходного кода

1. Клонируйте репозиторий:
   ```bash
   git clone <repository-url>
   cd GitHubActions
   ```

2. Соберите плагин:
   ```bash
   ./gradlew build
   ```

3. Установите плагин в IntelliJ IDEA:
   ```bash
   ./gradlew runIde
   ```

## Использование

1. Откройте проект с GitHub Actions workflow файлами
2. Перейдите в папку `.github/workflows/`
3. Откройте любой `.yml` или `.yaml` файл
4. Плагин автоматически откроет файл в режиме split-view с текстовым и визуальным редактором
5. Используйте кнопки в toolbar для переключения между режимами:
    - **Editor only**: Только текстовый редактор
    - **Preview only**: Только визуальное представление
    - **Editor and Preview**: Оба режима одновременно (по умолчанию)

## Поддерживаемые элементы workflow

### Triggers (on)

- `push` - с поддержкой branches, tags, paths
- `pull_request` - с поддержкой types, branches, paths
- `schedule` - cron выражения
- `workflow_dispatch` - ручной запуск
- `workflow_call` - вызов из других workflow
- Другие стандартные triggers

### Jobs

- Название и описание job
- `runs-on` - указание runner'а
- `needs` - зависимости между jobs
- `environment` - окружение
- `strategy.matrix` - матричные builds
- `container` и `services` - контейнеры
- `uses` - переиспользуемые workflow

### Steps

- `name` - название шага
- `uses` - использование готовых actions
- `run` - выполнение команд
- `with` - параметры для actions
- `env` - переменные окружения
- `if` - условия выполнения

## Структура проекта

```
src/main/kotlin/dev/meanmail/plugin/githubActions/
├── editor/
│   ├── WorkflowFileEditorProvider.kt      # Провайдер кастомного редактора
│   ├── WorkflowTextEditorWithPreview.kt   # Основной редактор с preview
│   └── WorkflowVisualEditor.kt             # Визуальный компонент
├── model/
│   └── WorkflowModel.kt                    # Модель данных workflow
└── parser/
    └── WorkflowParser.kt                   # Парсер YAML файлов
```

## Технические детали

- **Язык**: Kotlin
- **Платформа**: IntelliJ Platform 2024.3+
- **Парсер YAML**: SnakeYAML 2.2
- **Архитектура**: FileEditorProvider + TextEditorWithPreview

## Разработка

### Требования

- JDK 21+
- IntelliJ IDEA 2024.3+

### Запуск в режиме разработки

```bash
./gradlew runIde
```

### Сборка плагина

```bash
./gradlew buildPlugin
```

Готовый плагин будет в папке `build/distributions/`.

## Лицензия

Apache License 2.0

## Автор

meanmail.dev
