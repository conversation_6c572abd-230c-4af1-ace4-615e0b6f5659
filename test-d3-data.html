<!DOCTYPE html>
<html>
<head>
    <title>Test D3-DAG Data</title>
    <script src="https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3-dag@1.1.0/dist/d3-dag.min.js"></script>
</head>
<body>
    <div id="output"></div>
    <svg id="test-svg" width="800" height="600" style="border: 1px solid #ccc;"></svg>

    <script>
        console.log('Testing d3-dag...');
        console.log('d3 version:', d3.version);
        console.log('d3.dagStratify available:', typeof d3.dagStratify);
        console.log('d3.sugiyama available:', typeof d3.sugiyama);

        // Test data similar to what our plugin generates
        const testData = [
            {
                "id": "trigger",
                "parentIds": [],
                "label": "🚀 Triggers\nPush, PR",
                "color": "#238636",
                "width": 200,
                "height": 80
            },
            {
                "id": "test",
                "parentIds": ["trigger"],
                "label": "⚙️ Run Tests\nubuntu-latest\n2 steps",
                "color": "#1f6feb",
                "width": 200,
                "height": 80
            },
            {
                "id": "build",
                "parentIds": ["test"],
                "label": "⚙️ Build\nubuntu-latest\n3 steps",
                "color": "#1f6feb",
                "width": 200,
                "height": 80
            }
        ];

        console.log('Test data:', testData);

        try {
            // Create DAG
            const stratify = d3.dagStratify();
            const dag = stratify(testData);
            console.log('DAG created successfully:', dag);
            console.log('Nodes:', dag.descendants().length);

            // Apply layout
            const layout = d3.sugiyama()
                .size([700, 500])
                .nodeSize([220, 100]);
            
            layout(dag);
            console.log('Layout applied successfully');

            // Draw the DAG
            const svg = d3.select('#test-svg');
            
            // Create container
            const container = svg.append('g')
                .attr('transform', 'translate(50, 50)');

            // Draw links
            const links = container.selectAll('path')
                .data(dag.links())
                .enter().append('path')
                .attr('stroke', '#58a6ff')
                .attr('stroke-width', 2)
                .attr('fill', 'none')
                .attr('d', d => {
                    const source = d.source;
                    const target = d.target;
                    return `M${source.x},${source.y}L${target.x},${target.y}`;
                });

            // Draw nodes
            const nodes = container.selectAll('g.node')
                .data(dag.descendants())
                .enter().append('g')
                .attr('class', 'node')
                .attr('transform', d => `translate(${d.x},${d.y})`);

            nodes.append('rect')
                .attr('width', d => d.data.width)
                .attr('height', d => d.data.height)
                .attr('x', d => -d.data.width / 2)
                .attr('y', d => -d.data.height / 2)
                .attr('rx', 8)
                .attr('fill', d => d.data.color)
                .attr('stroke', '#30363d')
                .attr('stroke-width', 2);

            nodes.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '0.35em')
                .attr('fill', 'white')
                .text(d => d.data.label.split('\n')[0]);

            console.log('DAG drawn successfully!');
            document.getElementById('output').innerHTML = '<h2>Success! D3-DAG is working.</h2>';

        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').innerHTML = '<h2>Error: ' + error.message + '</h2>';
        }
    </script>
</body>
</html>
